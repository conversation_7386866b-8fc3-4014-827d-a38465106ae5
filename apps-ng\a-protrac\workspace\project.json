{"name": "pro-workspace", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-protrac/workspace/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-protrac/workspace/ng-package.json", "tsConfig": "a-protrac/workspace/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-protrac/workspace/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-protrac/workspace/jest.config.ts", "tsConfig": "a-protrac/workspace/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}