import { inject, Injectable } from '@angular/core';
import { CourseRunApiProxy, UpdateCourseRunCommand, UpdateCourseResult, CourseRunStructureDto } from '@ed/shared/data-academics';
import { Result } from '@tec/rad-core/utils';
import { CourseRunModel } from './model';

import { RadDataAccess } from '@tec/rad-core/services';

@Injectable({ providedIn: 'root' })
export class CourseRunFacade {
  #runApi = inject(CourseRunApiProxy);
  #dataAccess = inject(RadDataAccess);

  async getCourseStructure(courseId: string): Promise<CourseRunStructureDto> {

    const dto = await this.#dataAccess.getItem('CourseRunStructure', courseId, () => this.#runApi.getCourseRunStructure(courseId), n => n);

    
    return dto;
  }

  async getCourseRun(courseRunId: string): Promise<Result<CourseRunModel>> {
    const result = await Result.try$(this.#runApi.getCourseRun(courseRunId));

    return result.map<CourseRunModel>(n => new CourseRunModel(n.run));
    

  }

  async updateCourseRun(run: CourseRunModel): Promise<Result<UpdateCourseResult>> {
    const cmd = new UpdateCourseRunCommand();
    cmd.courseRunId = run.id;
    cmd.name = run.name;
    cmd.description = run.description;
    cmd.code =run.code;
    cmd.startDate = run.startDate;
    cmd.endDate = run.endDate;

   return await Result.try$(this.#runApi.updateCourseRun(cmd));
    
  }

  


}