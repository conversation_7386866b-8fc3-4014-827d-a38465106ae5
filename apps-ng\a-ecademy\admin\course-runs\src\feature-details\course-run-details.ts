import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input, OnInit } from '@angular/core';
import { RadContentLayout, RadDialogFooter, RadPage, RadPanel } from '@tec/rad-ui/layout';
import { RadLookupService } from '@tec/rad-xui/flex';
import { RadFormConfig, RadFormLayout } from '@tec/rad-xui/form';
import { RunDetailsController } from './course-run-details-controller';
import { injectRouteParam } from '@tec/rad-core/utils';

        
@Component({
    selector: 'ed-course-run-detail',
    imports: [CommonModule, RadPage, RadFormLayout,  RadPanel, RadDialogFooter, RadContentLayout],
    providers: [RunDetailsController],
    template: `
    <rad-page layout="card" title="Course Detail" footer="true" [loading]="controller.loading()">
      <rad-content-layout>
        <div class="flex flex-col space-y-6 p-6">
          <rad-panel title="Details">
            <rad-form-layout [form]="controller.form" [items]="fields" [model]="controller.courseRun()" />
          </rad-panel>
        </div>
        <rad-dialog-footer radContentFooter (confirm)="update()" confirmLabel="Update"></rad-dialog-footer>
      </rad-content-layout>
    </rad-page>
    `
    
})
export class CourseRunDetails implements OnInit {
        
  protected controller = inject(RunDetailsController);
  protected lookup = inject(RadLookupService);
  #id = injectRouteParam('courseRunId');

  fields: RadFormConfig = [
    {
      row: [
        { key: 'name', type: 'input', label: 'Name', span:6 },
        { key: 'code', type: 'input', label: 'Code', span:3, required:true },
       
      ],
    },
    {
      row: [
         { key: 'startDate', type: 'datepicker', span:3, required:true },
        { key: 'endDate', type: 'datepicker', span:3, required:true},
        
      ],
    },

    { key: 'description', type: 'textarea' },
  ];
        

  async ngOnInit(): Promise<void> {
    await this.controller.init(this.#id);
  }

  async update() {
   await this.controller.update()
    //await this.controller.courseFacade.updateCourse(course);
  }
        
}