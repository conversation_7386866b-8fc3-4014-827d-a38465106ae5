import { ModuleInfo } from '@tec/rad-core/abstractions';
import { radCompositeRoute } from '@tec/rad-core/composition';
import { courseRunResolverFn } from './feature-course-run';

export const CourseRunsModule: ModuleInfo = {
    name: 'CourseDeliveryModule',
    path: 'course-run',
    routes: [
        {
            name: 'CourseRun', path: ':courseRunId', data: {tabPage:true, title:'Course Run'},
            loadComponent: () => import('./feature-course-run').then(m => m.CourseRunPage),
            resolve: {structure: courseRunResolverFn},
            children: [
                ...radCompositeRoute('CourseRunDetails'),
            ]
        },
    ],
    views: [
        {name: 'CourseRuns', loadComponent: () => import('./feature-course-runs').then(m => m.CourseRunsList)},
        {name: 'CourseRunDetails', loadComponent: () => import('./feature-details').then(m => m.CourseRunDetails)},
        {name: 'RunSchedule', loadComponent: () => import('./feature-schedule').then(m => m.ScheduleList)}
    ]
};
