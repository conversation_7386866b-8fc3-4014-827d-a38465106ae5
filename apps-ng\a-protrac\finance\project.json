{"name": "pro-finance", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-protrac/finance/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-protrac/finance/ng-package.json", "tsConfig": "a-protrac/finance/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-protrac/finance/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-protrac/finance/jest.config.ts", "tsConfig": "a-protrac/finance/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}