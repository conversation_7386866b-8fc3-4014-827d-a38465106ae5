import { computed, inject, Injectable, resource } from "@angular/core";
import { CourseApiProxy, CreateCourseCommand } from "@ed/shared/data-academics"
import { apiResultFrom, executeApi, executeAsync } from "@tec/rad-core/utils";
import { CourseFacade } from "../core";
import { RadSignalBase } from "@tec/rad-core/services";


@Injectable()
export class CourseStore extends RadSignalBase {

    courseData = inject(CourseApiProxy);
    courseFacade = inject(CourseFacade);

    constructor() {
        super();
    }


    private _courses = resource({

        loader: () => executeApi(this.courseData.getCourses())

    })

    courses = computed(() => {
    
        if(this._courses.error()){
            return []
        }
        return this._courses.value()?.items;
    });
    loading = computed(() => this._courses.isLoading());


    async createCourse(command: CreateCourseCommand){

        const result = await apiResultFrom(this.courseData.createCourse(command));
        if(result.isSuccess){
            this._courses.reload();
        }

    }

}