import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { SegmentNode, SegmentType } from '@ed/shared/data-academics';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadPage } from '@tec/rad-ui/layout';
import { TreeGridAllModule } from '@syncfusion/ej2-angular-treegrid';
import { ScheduleListController } from './schedule-list-controller';
import { RadButtonModule } from '@tec/rad-ui/component';
import { SplitButtonModule } from 'primeng/splitbutton';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'ed-schedule-list',
  imports: [CommonModule, RadPage, TreeGridAllModule, RadButtonModule, SplitButtonModule],
  providers: [ScheduleListController],
  template: `
    <rad-page title="Schedule" [loading]="controller.isLoading()">
      <ejs-treegrid
        [dataSource]="data()"
        allowPaging="true"
        height="100%"
        idMapping="id"
        parentIdMapping="parentId"
        [treeColumnIndex]="0"
        rowHeight="50"
      >
        <e-columns>
          <e-column field="name" headerText="Task Name" width="170"></e-column>
          <e-column field="startDate" headerText="Start Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column field="endDate" headerText="End Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column width="150">
            <ng-template #template let-data>
              @let item = data;
              <div class="flex flex-row justify-end">
                @if(data.containerType === 'Segment') {
                  @let childTypes = getChildSegmentTypes(data.segmentTypeId);
                  @if(childTypes.length === 0) {
                    <!-- No child types available -->
                  } @else if(childTypes.length === 1) {
                    <!-- Single child type - show normal button -->
                    <p-button
                      [label]="'Add ' + childTypes[0].name"
                      icon="ri-add-line"
                      size="small"
                      outlined="true"
                      (onClick)="addSegmentOfType(data.segmentTypeId, childTypes[0])"
                    />
                  } @else {
                    <!-- Multiple child types - show split button -->
                    <p-splitbutton
                      [label]="'Add ' + childTypes[0].name"
                      icon="ri-add-line"
                      size="small"
                      outlined="true"
                      [model]="getChildSegmentTypeOptions(data.segmentTypeId)"
                      (onClick)="addSegmentOfType(data.segmentTypeId, childTypes[0])"
                    />
                  }
                }
              </div>
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-treegrid>
    </rad-page>
  `,
})
export class ScheduleList implements OnInit {
  protected controller = inject(ScheduleListController);
  #id = injectRouteParam('courseRunId');
  data = this.controller.nodes;

  ngOnInit(): void {
    this.controller.init(this.#id);
  }

  getChildSegmentTypes(segmentTypeId: string): SegmentType[] {
    const allTypes = this.controller.types();
    return allTypes.filter(type => type.parentId === segmentTypeId);
  }

  getChildSegmentTypeOptions(segmentTypeId: string): MenuItem[] {
    const childTypes = this.getChildSegmentTypes(segmentTypeId);

    // Skip the first item since it's used as the main button action
    return childTypes.slice(1).map(type => ({
      label: type.name || 'Unnamed Type',
      icon: 'ri-add-line',
      command: () => this.addSegmentOfType(segmentTypeId, type)
    }));
  }

  addSegmentOfType(parentSegmentTypeId: string, segmentType: SegmentType): void {
    // Handle dropdown option click - add segment of specific type
    console.log('Adding segment of type:', segmentType.name, 'for parent:', parentSegmentTypeId);
    // TODO: Implement specific segment type creation logic
  }
}
