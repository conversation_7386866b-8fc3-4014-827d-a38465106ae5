import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';
import { RadButtonModule } from '@tec/rad-ui/component';
import { ButtonContext } from '@tec/rad-core/abstractions';
import { ActionItem } from '@tec/rad-ui/common';

@Component({
  selector: 'rad-popup-menu',
  template: `
    <p-menu #menu [model]="menuItems()" [popup]="true" appendTo="body" />
    <p-button
      radButton
      [display]="context()"
      (click)="!showOnHover() && menu.toggle($event)"
      (mouseenter)="showOnHover() && menu.show($event)"
      (mouseleave)="showOnHover() && menu.hide()"
      [label]="label()"
      [icon]="icon()"
    />`,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    MenuModule,
    RadButtonModule,
    //Add required imports here
  ],
})
export class RadPopupMenu {
  icon = input<string>('pi pi-bars');
  context = input<ButtonContext>('secondary');
  label = input<string>('');
  showOnHover = input<boolean>(false);

  items = input<ActionItem[]>();

  protected menuItems = computed(() => {
    return this.items().map((item) => this.toMenuItem(item));
  });

  private toMenuItem(action: ActionItem): MenuItem {
    const item: MenuItem = {
      label: action.label,
      icon: action.icon,
      command: () => action.action(),
    };

    return item;
  }
}
