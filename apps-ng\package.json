{"name": "@tec/apps", "version": "0.0.0", "license": "MIT", "scripts": {"debug:ed-admin": "nx serve ed-admin-app", "debug:protrac": "nx serve protrac-app", "build:ed-admin": "nx build ed-admin-app --prod --base-href='/admin/'", "nswag:ecademy": "node ./a-ecademy/ecademy-nswag.js", "debug:tec-template": "nx serve template-app --verbose", "nswag:tec": "node ./c-tec/tec-nswag.js", "reinstall": "powershell -Command \"Remove-Item -Recurse -Force node_modules, pnpm-lock.yaml; pnpm install\""}, "private": true, "dependencies": {"@abp/ng.account.core": "9.3.1", "@abp/ng.components": "9.3.1", "@abp/ng.core": "9.3.1", "@abp/ng.oauth": "9.3.1", "@abp/ng.setting-management": "9.3.1 ", "@angular-architects/module-federation": "~20.0.0", "@angular/animations": "~20.2.0", "@angular/cdk": "~20.2.0", "@angular/common": "~20.2.0", "@angular/compiler": "~20.2.0", "@angular/core": "~20.2.0", "@angular/forms": "~20.2.0", "@angular/material": "~20.2.0", "@angular/platform-browser": "~20.2.0", "@angular/platform-browser-dynamic": "~20.2.0", "@angular/router": "~20.2.0", "@microsoft/signalr": "^8.0.7", "@ngrx/signals": "19.2.1", "@ngx-formly/core": "7.0.0", "@ngx-formly/material": "7.0.0", "@ngx-formly/primeng": "7.0.0", "@ngx-formly/schematics": "7.0.0", "@primeng/themes": "20.0.1", "@syncfusion/ej2-base": "^30.1.37", "@syncfusion/ej2-angular-calendars": "^30.1.37", "@syncfusion/ej2-angular-charts": "^30.1.39", "@syncfusion/ej2-angular-dropdowns": "^30.1.39", "@syncfusion/ej2-angular-grids": "^30.1.39", "@syncfusion/ej2-angular-navigations": "^30.1.39", "@syncfusion/ej2-angular-schedule": "^30.1.39", "@syncfusion/ej2-angular-treegrid": "^30.1.39", "@tailwindcss/postcss": "^4.1.5", "@volo/abp.ng.account": "9.3.1", "@volo/abp.ng.identity": "9.3.1", "@volo/abp.ng.saas": "9.3.1", "apexcharts": "^3.45.0", "lodash-es": "4.17.21", "ngx-debounce": "^2.0.1", "ngx-logger": "^5.0.12", "ngx-signal-operators": "^0.1.0", "perfect-scrollbar": "1.5.5", "primeicons": "^7.0.0", "primeng": "20.0.0-rc.3", "quill": "^2.0.2", "reflect-metadata": "^0.2.2", "rxjs": "~7.8.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/core": "~20.2.0", "@angular-devkit/schematics": "~20.2.0", "@angular/build": "~20.2.0", "@angular/cli": "~20.2.0", "@angular/compiler-cli": "~20.2.0", "@angular/language-service": "~20.2.0", "@eslint/js": "^9.8.0", "@faker-js/faker": "9.4.0", "@nx/angular": "21.4.1", "@nx/devkit": "21.4.1", "@nx/eslint": "21.4.1", "@nx/eslint-plugin": "21.4.1", "@nx/jest": "21.4.1", "@nx/js": "21.4.1", "@nx/playwright": "21.4.1", "@nx/web": "21.4.1", "@nx/workspace": "21.4.1", "@playwright/test": "^1.36.0", "@schematics/angular": "~20.1.0", "@softarc/eslint-plugin-sheriff": "^0.19.2", "@softarc/sheriff-core": "^0.19.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.29.0", "angular-eslint": "^20.0.0", "autoprefixer": "^10.4.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "~14.6.0", "jest-util": "^30.0.2", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "20.2.0", "nswag": "^14.0.7", "nx": "21.4.1", "postcss": "^8.5.6", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "tailwindcss": "3.4.17", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "verdaccio": "^6.0.5"}, "nx": {"includedScripts": []}}