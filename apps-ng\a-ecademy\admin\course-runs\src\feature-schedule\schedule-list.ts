import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { SegmentNode, SegmentType } from '@ed/shared/data-academics';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadPage } from '@tec/rad-ui/layout';
import { TreeGridAllModule } from '@syncfusion/ej2-angular-treegrid';
import { ScheduleListController } from './schedule-list-controller';
import { RadPopupMenu } from '@tec/rad-ui/menu';
import { ActionItem } from '@tec/rad-ui/common';

@Component({
  selector: 'ed-schedule-list',
  imports: [CommonModule, RadPage, TreeGridAllModule, RadPopupMenu],
  providers: [ScheduleListController],
  template: `
    <rad-page title="Schedule" [loading]="controller.isLoading()">
      <ejs-treegrid
        [dataSource]="data()"
        allowPaging="true"
        height="100%"
        idMapping="id"
        parentIdMapping="parentId"
        [treeColumnIndex]="0"
        rowHeight="50"
      >
        <e-columns>
          <e-column field="name" headerText="Task Name" width="170"></e-column>
          <e-column field="startDate" headerText="Start Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column field="endDate" headerText="End Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column width="100">
            <ng-template #template let-data>
              @let item = data;
              <div class="flex flex-row justify-end">
                @if(data.containerType === 'Segment') {
                  @let childTypes = getChildSegmentTypes(data.segmentTypeId);
                  @if(childTypes.length > 0) {
                    <rad-popup-menu
                      icon="ri-add-line"
                      context="secondary"
                      [showOnHover]="true"
                      [items]="getChildSegmentTypeActions(data.segmentTypeId)"
                    />
                  }
                }
              </div>
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-treegrid>
    </rad-page>
  `,
})
export class ScheduleList implements OnInit {
  protected controller = inject(ScheduleListController);
  #id = injectRouteParam('courseRunId');
  data = this.controller.nodes;

  ngOnInit(): void {
    this.controller.init(this.#id);
  }

  getChildSegmentTypes(segmentTypeId: string): SegmentType[] {
    const allTypes = this.controller.types();
    return allTypes.filter(type => type.parentId === segmentTypeId);
  }

  getChildSegmentTypeActions(segmentTypeId: string): ActionItem[] {
    const childTypes = this.getChildSegmentTypes(segmentTypeId);

    return childTypes.map(type => ({
      label: type.name || 'Unnamed Type',
      icon: 'ri-add-line',
      action: () => this.addSegmentOfType(segmentTypeId, type)
    }));
  }

  addSegmentOfType(parentSegmentTypeId: string, segmentType: SegmentType): void {
    // Handle dropdown option click - add segment of specific type
    console.log('Adding segment of type:', segmentType.name, 'for parent:', parentSegmentTypeId);
    // TODO: Implement specific segment type creation logic
  }
}
