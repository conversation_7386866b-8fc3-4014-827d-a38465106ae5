{"name": "pro-shared-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-protrac/shared-ui/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-protrac/shared-ui/ng-package.json", "tsConfig": "a-protrac/shared-ui/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-protrac/shared-ui/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-protrac/shared-ui/jest.config.ts", "tsConfig": "a-protrac/shared-ui/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}