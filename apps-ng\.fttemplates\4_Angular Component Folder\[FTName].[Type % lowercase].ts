import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';

        
@Component({
    selector: 'app-<FTName>',
    templateUrl: './<FTName>.<Type | lowercase>.html',
    styleUrls: ['./<FTName>.<Type | lowercase>.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports:[
        CommonModule
        //Add required imports here
    ]
})
export class <FTName | pascalcase><Type | pascalcase> {
        

        

        
}