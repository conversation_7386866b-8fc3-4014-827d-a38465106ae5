{"name": "pro-shared", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-protrac/shared/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-protrac/shared/ng-package.json", "tsConfig": "a-protrac/shared/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-protrac/shared/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-protrac/shared/jest.config.ts", "tsConfig": "a-protrac/shared/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}