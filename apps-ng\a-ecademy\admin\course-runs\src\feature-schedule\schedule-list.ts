import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { SegmentNode, SegmentType } from '@ed/shared/data-academics';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadPage } from '@tec/rad-ui/layout';
import { TreeGridAllModule } from '@syncfusion/ej2-angular-treegrid';
import { ScheduleListController } from './schedule-list-controller';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'ed-schedule-list',
  imports: [CommonModule, RadPage, TreeGridAllModule, ButtonModule, TooltipModule],
  providers: [ScheduleListController],
  styles: [`
    /* Remove focus outline and selection styling from TreeGrid cells */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell:focus,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-focused,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-cellselected,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-active {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      background-color: transparent !important;
    }

    /* Remove focus styling from rows */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row.e-focused,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row.e-selected {
      outline: none !important;
      box-shadow: none !important;
      background-color: transparent !important;
    }

    /* Prevent any focus indicators on the grid itself */
    ::ng-deep .e-treegrid:focus,
    ::ng-deep .e-treegrid .e-gridcontent:focus {
      outline: none !important;
    }

    /* Make cells non-selectable */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  `],
  template: `
    <rad-page title="Schedule" [loading]="controller.isLoading()">
      <ejs-treegrid
        [dataSource]="data()"
        [allowPaging]="false"
        [allowSelection]="false"
        height="100%"
        idMapping="id"
        parentIdMapping="parentId"
        [treeColumnIndex]="0"
        rowHeight="50"
      >
        <e-columns>
          <e-column field="name" headerText="Task Name" width="170"></e-column>
          <e-column field="startDate" headerText="Start Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column field="endDate" headerText="End Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column width="100">
            <ng-template #template let-data>
              @let item = data;
              <div class="flex flex-row justify-end">
                @if(data.containerType === 'Segment') {
                  @let childType = getChildSegmentType(data.segmentTypeId);
                  @if(childType) {
                    <p-button
                      icon="ri-add-line"
                      size="small"
                      outlined="true"
                      [pTooltip]="'Add ' + childType.name"
                      tooltipPosition="left"
                      (onClick)="addSegmentOfType(data.segmentTypeId, childType)"
                    />
                  }
                }
              </div>
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-treegrid>
    </rad-page>
  `,
})
export class ScheduleList implements OnInit {
  protected controller = inject(ScheduleListController);
  #id = injectRouteParam('courseRunId');
  data = this.controller.nodes;

  ngOnInit(): void {
    this.controller.init(this.#id);
  }

  getChildSegmentType(segmentTypeId: string): SegmentType | undefined {
    const allTypes = this.controller.types();
    return allTypes.find(type => type.parentId === segmentTypeId);
  }

  addSegmentOfType(parentSegmentTypeId: string, segmentType: SegmentType): void {
    // Handle dropdown option click - add segment of specific type
    console.log('Adding segment of type:', segmentType.name, 'for parent:', parentSegmentTypeId);
    // TODO: Implement specific segment type creation logic
  }
}
