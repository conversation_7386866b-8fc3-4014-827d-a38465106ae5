import { Course, CourseStructureDto } from "@ed/shared/data-academics";


export class CourseModel extends Course {

    constructor(dto?: Course) {
        super(dto);
    }


    static fromDto(dto: Course): CourseModel {
        return new CourseModel(dto);
    }

}

export class CourseStructureModel extends CourseStructureDto {

    constructor(dto?: CourseStructureDto) {
        super(dto);
    }

    
}



