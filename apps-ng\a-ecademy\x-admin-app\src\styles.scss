@use '../../../c-tec/rad-ui/theme/evo/styles.scss' as *;
@use '../../../c-tec/rad-app/styles/styles.scss' as *;
@use '../../../c-tec/rad-xui/styles/styles.scss' as *;
@use '@syncfusion/ej2-base/styles/tailwind.css' as *;  
@use '@syncfusion/ej2-buttons/styles/tailwind.css' as *;  
@use '@syncfusion/ej2-calendars/styles/tailwind.css'  as *;  
@use '@syncfusion/ej2-dropdowns/styles/tailwind.css' as *;  
@use '@syncfusion/ej2-inputs/styles/tailwind.css' as *;  
@use '@syncfusion/ej2-navigations/styles/tailwind.css' as *;
@use '@syncfusion/ej2-popups/styles/tailwind.css' as *;
@use '@syncfusion/ej2-splitbuttons/styles/tailwind.css' as *;
@use '@syncfusion/ej2-notifications/styles/tailwind.css' as *;
@use '@syncfusion/ej2-angular-grids/styles/tailwind.css' as *;
@use '@syncfusion/ej2-treegrid/styles/tailwind.css' as *;
@use "quill/dist/quill.core.css" as *;
@use "quill/dist/quill.snow.css" as *;
@use "quill/dist/quill.bubble.css" as *;
