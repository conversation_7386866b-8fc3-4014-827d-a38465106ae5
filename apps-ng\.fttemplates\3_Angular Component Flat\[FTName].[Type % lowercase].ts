import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';

        
@Component({
    selector: 'app-<FTName>',
    templateUrl: './<FTName>.<Type | lowercase>.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports:[
        CommonModule
        //Add required imports here
    ],
    styles: []
})
export class <FTName | pascalcase><Type | pascalcase> {
        

        

        
}