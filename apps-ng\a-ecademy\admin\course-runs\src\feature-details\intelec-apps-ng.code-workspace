{"folders": [{"path": "../../../../.."}], "settings": {"explorer.fileNesting.expand": false, "angular.enable-strict-mode-prompt": false, "nxConsole.generateAiAgentRules": true, "typescript.preferences.includePackageJsonAutoImports": "on", "debug.javascript.autoAttachFilter": "smart", "debug.javascript.terminalOptions": {"skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, "debug.javascript.autoAttachSmartPattern": ["${workspaceFolder}/**", "!**/node_modules/**", "**/$KNOWN_TOOLS$/**"]}}