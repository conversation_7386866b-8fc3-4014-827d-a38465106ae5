import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { RouterModule } from '@angular/router';
import { CourseRunStructureDto } from '@ed/shared/data-academics';
import { INavItem, injectTabReference } from '@tec/rad-core/abstractions';
import { injectRouteData, injectRouteParam } from '@tec/rad-core/utils';
import {
  RadContentLayout,
  RadHeader,
  RadRouterOutlet,
  RadSideMaster,
  RadSideNav,
} from '@tec/rad-ui/layout';
import { RadSidebar } from '@tec/rad-ui/navbar';

@Component({
  selector: 'ed-course-run',

  imports: [
    CommonModule,
    RadSideNav,
    RadSidebar,
    RouterModule,
    RadHeader,
    RadContentLayout,
    //Add required imports here
    RadRouterOutlet,
  ],
  template: `
    <rad-side-nav [divider]="true" drawerWidth="180">
      <rad-sidebar
        radSideNavDrawer
        side
        class="h-full overflow-clip bg-slate-50"
        [appearance]="'default'"
        [navigation]="nav"
        [inner]="true"
        [mode]="'side'"
        name="course-sidebar-navigation"
        [opened]="true"
      >
        <rad-header
          radSidebarHeader
          radContentHeader
          [title]="data?.name"
          [divider]="true"
        >
        </rad-header>
      </rad-sidebar>

      <rad-router-outlet></rad-router-outlet>
      <!-- <div class="flex flex-col flex-auto animation-2">
      <router-outlet *ngIf="true"></router-outlet>
    </div>
     -->
    </rad-side-nav>
  `,
})
export class CourseRunPage implements OnInit {
  #id = injectRouteParam('courseRunId');
  #tabRef = injectTabReference();
  data = injectRouteData<CourseRunStructureDto>('structure');
  protected nav: INavItem[] = [
    {
      title: 'Details',
      icon: 'ri-information-line',
      link: 'view/CourseRunDetails',
    },
    {
      title: 'Schedule',
      icon: 'ri-calendar-line',
      link: 'view/RunSchedule',
    },
  ];

  ngOnInit(): void {
    if(this.#tabRef){
        this.#tabRef.setTitle(this.data.code);
    }
  }
}
