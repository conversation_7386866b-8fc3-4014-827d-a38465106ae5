//resolve the course id from the route and load the course data
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { CourseRunFacade } from '../core';
import { inject } from '@angular/core';
import { injectRouteParam } from '@tec/rad-core/utils';


  
  export const courseRunResolverFn = (
    route: ActivatedRouteSnapshot,
    
  ) => {
    const facade = inject(CourseRunFacade)
    const courseRunId = route.params['courseRunId'];
    return facade.getCourseStructure(courseRunId);
  };