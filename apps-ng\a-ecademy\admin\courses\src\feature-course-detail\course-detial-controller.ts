import { computed, inject, Injectable, signal } from "@angular/core";
import { IServiceState } from "@tec/rad-core/abstractions";
import { RadSignalBase } from "@tec/rad-core/services";
import { CourseFacade, CourseModel } from "../core";
import { injectRouteParam } from "@tec/rad-core/utils";
import { RadFormData } from "@tec/rad-xui/form";

interface DetailState extends IServiceState {
    course: CourseModel
}

const initialState: DetailState = {
    course: undefined
}


@Injectable()
export class CourseDetialController extends RadSignalBase<DetailState>{

    private courseFacade = inject(CourseFacade)
    private courseId = injectRouteParam('courseId')

    

    course = computed(()=>{

        const course = this.state.course();
        if(course){
          
          return course;
        }

        return undefined;
});

    readonly form = new RadFormData(this.course, signal(true));

    constructor() {
        super(initialState);
    }

    async init(courseId: string){
        this.courseId = courseId;
        const courseResult = await this.execute(()=> this.courseFacade.getCourse(this.courseId));
        if(courseResult.isSuccess){
            this.patchState({course: courseResult.value});
        }
        
    
        
    }

    async update() {
        if(!this.form.validate()){
            return;
        }

        const course = this.course();
        const result = await this.execute(()=> this.courseFacade.updateCourse(course));
        if(result.isSuccess){
            this.notifySuccess('Course updated');
        }
    }

}