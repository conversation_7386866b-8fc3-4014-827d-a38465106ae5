import { computed, inject, Injectable, resource, signal } from '@angular/core';
import { injectRouteParam } from '@tec/rad-core/utils';
import { ScheduleManager } from './services/schedule-manager';
import { RadSignalBase } from '@tec/rad-core/services';

@Injectable()
export class ScheduleListController extends RadSignalBase {
  #scheduleManager = inject(ScheduleManager);
  private courseRunId = signal('');
  private _nodes = resource({
    params: () => this.courseRunId,
    loader: (r) => {
      return this.#scheduleManager.getSegmentTree(r.params());
    },
  });

  nodes = computed(() => {
    if (!this._nodes.hasValue() || this._nodes.error()) {
      return [];
    }
    const nodes =  this._nodes.value();
    return nodes?.value;
  });

  private _types = resource({
    loader: () => {
      return this.#scheduleManager.getSegmentTypes();
    },
  });

  types = computed(() => {
    if (!this._types.hasValue() || this._types.error()) {
      return [];
    }
    const types =  this._types.value();
    return types?.value;
  });

  isLoading = computed(() => this._nodes.isLoading() || this.loading());

  constructor() {
    super();
  }

  init(courseRunId: string) {
    this.courseRunId.set(courseRunId);
  }

  
}
