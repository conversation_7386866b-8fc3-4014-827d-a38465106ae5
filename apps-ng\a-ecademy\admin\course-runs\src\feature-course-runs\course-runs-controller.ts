import { computed, inject, Injectable, resource } from "@angular/core";
import { CourseRunApiProxy, CreateCourseRunCommand } from "@ed/shared/data-academics";
import { apiResultFrom, executeApi, injectRouteParam } from "@tec/rad-core/utils";
import { RadFormConfig } from "@tec/rad-xui/form";
import { RadDialogFormManager } from "@tec/rad-xui/services";
import { RadSignalBase } from "@tec/rad-core/services";

@Injectable()
export class CourseRunsController extends RadSignalBase {

    #runsApi = inject(CourseRunApiProxy);
    #formManager = inject(RadDialogFormManager);
    private courseId = injectRouteParam("courseId");
    

    constructor() {
        super();
    }

    private _runs = resource({
        params: ()=> this.courseId,
        loader: (params) =>{
            return executeApi(this.#runsApi.getCourseRuns(params.params))
        } 
    })  

    runs = computed(() => this._runs.value()?.items);
    loading = computed(() => this._runs.isLoading());



    private createFields: RadFormConfig = [
        { key: 'name', label: 'Name', type: 'input', required: true},
        { key: 'description', label: 'Description', type: 'textarea'},
        {
            row: [
                { key: 'startDate', label: 'Start Date', type: 'datepicker', span:6, required: true},
                { key: 'endDate', label: 'End Date', type: 'datepicker', span:6, required: true},
            ]
        }
       
    ]


    async createRun(){
        const model = new CreateCourseRunCommand();
        model.courseId = this.courseId;
        const dialogResult =  await this.#formManager.showForm(model,  this.createFields, "Create Course Run");
        if(dialogResult.cancelled){
            return;
        }
        const createResult = await this.execute$(this.#runsApi.createCourseRun(dialogResult.value));
        if(createResult.isSuccess){
            this.notifySuccess('Course Run created');
        }

        this._runs.reload();
    }

}