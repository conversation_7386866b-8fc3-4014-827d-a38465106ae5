{"name": "pro-admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-protrac/admin/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-protrac/admin/ng-package.json", "tsConfig": "a-protrac/admin/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-protrac/admin/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-protrac/admin/jest.config.ts", "tsConfig": "a-protrac/admin/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}