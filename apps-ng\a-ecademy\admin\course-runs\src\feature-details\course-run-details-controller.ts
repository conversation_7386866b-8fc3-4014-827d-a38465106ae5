import { inject, Injectable, signal } from '@angular/core';
import { IServiceState } from '@tec/rad-core/abstractions';
import { CourseRunModel } from '../core/model';
import { CourseRunFacade } from '../core/course-run-facade';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadSignalBase } from '@tec/rad-core/services';
import { RadFormData } from '@tec/rad-xui/form';

interface DetailState extends IServiceState {
  courseRun: CourseRunModel;
}

const initialState: DetailState = {
  courseRun: undefined,
};

@Injectable()
export class RunDetailsController extends RadSignalBase<DetailState> {
  private runFacade = inject(CourseRunFacade);
  private runId = injectRouteParam('courseRunId');

  courseRun = this.select((n) => n.courseRun());

  readonly form = new RadFormData(this.courseRun, signal(true));

  constructor() {
    super(initialState);
  }

  async init(runId: string) {
    this.runId = runId;
    const result = await this.execute(() =>
      this.runFacade.getCourseRun(this.runId)
    );
    if (result.isSuccess) {
      this.patchState({ courseRun: result.value });
    }
  }

  async update() {
    if (!this.form.validate()) {
      return;
    }

    const courseRun = this.courseRun();
    const result = await this.execute(() =>
      this.runFacade.updateCourseRun(courseRun)
    );
    if (result.isSuccess) {
      this.notifySuccess('Course run updated');
    }
  }
}
