import { inject, Injectable } from '@angular/core';
import { CourseApiProxy, CourseStructureDto, CreateCourseCommand, UpdateCourseCommand, UpdateCourseResult } from '@ed/shared/data-academics';
import { ApiResult, executeApi, Result } from '@tec/rad-core/utils';
import { CourseModel, CourseStructureModel } from './course-types';
import { UpdateCourseAction } from './course-actions';
import { first, firstValueFrom } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CourseFacade {
  #courseApi = inject(CourseApiProxy);

  async getCourseStructure(courseId: string): Promise<CourseStructureModel> {
    const dto = await executeApi(this.#courseApi.getCourseStructure(courseId));
    return new CourseStructureModel(dto);
  }

  async getCourse(courseId: string): Promise<Result<CourseModel>> {
    const result = await Result.try$(this.#courseApi.getCourse(courseId));
    return result.map<CourseModel>(n => new CourseModel(n.course));
    

  }

  async updateCourse(course: CourseModel): Promise<Result<UpdateCourseResult>> {
    const cmd = new UpdateCourseCommand();
    cmd.courseId = course.id;
    cmd.name = course.name;
    cmd.description = course.description;
    cmd.gradeId = course.gradeId;
    cmd.status = course.status;
    cmd.courseCode = course.courseCode;

    return Result.try$(this.#courseApi.updateCourse(cmd));
    
  }

  async createCourse(action: CreateCourseCommand): Promise<string> {
    const result = await executeApi(this.#courseApi.createCourse(action));
    return result.id;
  }


}
