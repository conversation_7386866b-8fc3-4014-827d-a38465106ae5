import { inject, Injectable } from "@angular/core";
import { ScheduleApiProxy, SegmentNode, SegmentType, SegmentTypeApiProxy } from "@ed/shared/data-academics";
import { RadDataAccess } from "@tec/rad-core/services";
import { Result, executeApi } from "@tec/rad-core/utils";




@Injectable({providedIn:'root'})
export class ScheduleManager {

    #scheduleApi = inject(ScheduleApiProxy);
    #segmentTypeApi = inject(SegmentTypeApiProxy);
    #dataAccess = inject(RadDataAccess);

    async getSegmentTree(courseRunId: string): Promise<Result<SegmentNode[]>> {
        const result = await Result.try$(this.#scheduleApi.getSegmentNodes(courseRunId));
        return result.map(n => n.items);

    }


    async getSegmentTypes(): Promise<Result<SegmentType[]>> {
        const result = await Result.try$(this.#segmentTypeApi.getList());
        return result.map(n => n.items.sort((a, b) => a.name?.localeCompare(b.name)));
    }

    async getChildSegmentTypes(parentId: string): Promise<SegmentType[]> {
        const types = (await this.getSegmentTypes());
        const items = types.map(n => n.filter(t => t.parentId === parentId));
        return items.value;
      
    }

}