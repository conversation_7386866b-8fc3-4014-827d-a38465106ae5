import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { SegmentNode, SegmentType } from '@ed/shared/data-academics';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadPage } from '@tec/rad-ui/layout';
import { TreeGridAllModule } from '@syncfusion/ej2-angular-treegrid';
import { RowDragEventArgs } from '@syncfusion/ej2-treegrid';
import { ScheduleListController } from './schedule-list-controller';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { AppNavigationService } from '@tec/rad-core/abstractions';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'ed-schedule-list',
  imports: [CommonModule, RadPage, TreeGridAllModule, ButtonModule, TooltipModule],
  providers: [ScheduleListController],
  styles: [`
    /* Remove focus outline and selection styling from TreeGrid cells */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell:focus,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-focused,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-cellselected,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell.e-active {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      background-color: transparent !important;
    }

    /* Remove focus styling from rows */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row.e-focused,
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row.e-selected {
      outline: none !important;
      box-shadow: none !important;
      background-color: transparent !important;
    }

    /* Prevent any focus indicators on the grid itself */
    ::ng-deep .e-treegrid:focus,
    ::ng-deep .e-treegrid .e-gridcontent:focus {
      outline: none !important;
    }

    /* Make cells non-selectable */
    ::ng-deep .e-treegrid .e-gridcontent .e-table .e-row .e-rowcell {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  `],
  template: `
    <rad-page title="Schedule" [loading]="controller.isLoading()">
      <ejs-treegrid
        [dataSource]="data()"
        [allowPaging]="false"
        [allowSelection]="false"
        [allowRowDragAndDrop]="true"
        height="100%"
        idMapping="id"
        parentIdMapping="parentId"
        [treeColumnIndex]="0"
        rowHeight="50"
        (rowDrop)="onRowDrop($event)"
        (rowDragStart)="onRowDragStart($event)"
        (rowDragStartHelper)="onRowDragStartHelper($event)"
        
      >
        <e-columns>
          <e-column headerText="Task Name" width="170">
            <ng-template #template let-data>
              <span
                class="cursor-pointer hover:text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded"
                tabindex="0"
                role="button"
                (click)="viewDetails(data)"
                (keydown.enter)="editNode(data)"
                (keydown.space)="editNode(data)"
                [title]="'Click to edit ' + data.name"
                [attr.aria-label]="'Edit ' + data.name"
              >
                {{ data.name }}
              </span>
            </ng-template>
          </e-column>
          <e-column field="startDate" headerText="Start Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column field="endDate" headerText="End Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column width="120">
            <ng-template #template let-data>
              @let item = data;
              <div class="flex flex-row justify-end gap-2">
                

                <!-- Add Child Segment Button -->
                @if(data.containerType === 'Segment') {
                  @let childType = getChildSegmentType(data.segmentTypeId);
                  @if(childType) {
                    <p-button
                      icon="ri-add-line"
                      size="small"
                      outlined="true"
                      [pTooltip]="'Add ' + childType.name"
                      tooltipPosition="left"
                      (onClick)="addSegmentOfType(data.segmentTypeId, childType)"
                    />
                  }
                }
                <!-- View Details Button -->
                <p-button
                  icon="ri-eye-line"
                  size="small"
                  outlined="true"
                  severity="secondary"
                  [pTooltip]="'View details'"
                  tooltipPosition="left"
                  (onClick)="viewDetails(data)"
                />
              </div>
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-treegrid>
    </rad-page>
  `,
})
export class ScheduleList implements OnInit {
  protected controller = inject(ScheduleListController);
  #id = injectRouteParam('courseRunId');
  #nav = inject(AppNavigationService);
  data = this.controller.nodes;
  activatedRoute = inject(ActivatedRoute);

  ngOnInit(): void {
    this.controller.init(this.#id);
  }

  getChildSegmentType(segmentTypeId: string): SegmentType | undefined {
    const allTypes = this.controller.types();
    return allTypes?.find(type => type.parentId === segmentTypeId);
  }

  addSegmentOfType(parentSegmentTypeId: string, segmentType: SegmentType): void {
    // Handle dropdown option click - add segment of specific type
    console.log('Adding segment of type:', segmentType.name, 'for parent:', parentSegmentTypeId);
    // TODO: Implement specific segment type creation logic
  }

  

  async viewDetails(node: SegmentNode): Promise<void> {
    // Handle view details button click
    console.log('Viewing details for node:', node.name, 'ID:', node.id);
    // TODO: Implement view details logic (e.g., open details dialog, navigate to details page, etc.)
    await this.#nav.goToSubView( this.activatedRoute, 'RunSegment', node.id);
  }

  onRowDragStart(args: RowDragEventArgs): void {
    // Called when drag starts
    console.log('Drag started for:', args.data);
  }

  onRowDragStartHelper(args: RowDragEventArgs): void {
    // Called to customize drag helper
    console.log('Drag helper for:', args.data);
  }

  onRowDrop(args: RowDragEventArgs): void {
    // Handle row drop event with validation
    console.log('Row drop event:', args);

    const draggedNode = args.data[0] as SegmentNode;

    // Get the target node from the drop target element
    // We need to find the target node data from the TreeGrid
    let targetNode: SegmentNode | null = null;
    if (args.target) {
      // Find the target row data based on the drop target
      const allData = this.data();
      const targetRowIndex = args.dropIndex;
      if (targetRowIndex !== undefined && targetRowIndex >= 0 && targetRowIndex < allData.length) {
        targetNode = allData[targetRowIndex] as SegmentNode;
      }
    }

    // Validate if the drop is allowed
    if (!this.canDropNode(draggedNode, targetNode)) {
      
      // Cancel the drop operation
      console.log('Drop cancelled: Invalid drop target');
      // Note: TreeGrid doesn't have args.cancel, we need to prevent the default behavior
      if (args.originalEvent && 'preventDefault' in args.originalEvent) {
        (args.originalEvent as Event).preventDefault();
      }
      return;
    }

    console.log(`Moving ${draggedNode.name} to ${targetNode?.name || 'root'}`);
    // TODO: Implement actual node reordering logic
  }

  private canDropNode(draggedNode: SegmentNode, targetNode: SegmentNode | null): boolean {
    // If dropping to root level, allow it
    if (!targetNode) {
      return true;
    }

    // Get the segment types
    const allTypes = this.controller.types();
    if (!allTypes) {
      return false;
    }

    // Find the dragged node's segment type
    const draggedNodeType = allTypes.find(type => type.id === draggedNode.segmentTypeId);
    if (!draggedNodeType) {
      return false;
    }

    // Check if the target node's segmentTypeId matches the dragged node's parentTypeId
    const canDrop = targetNode.segmentTypeId === draggedNode.parentTypeId;

    console.log(`Can drop ${draggedNode.name} (type: ${draggedNodeType.name}, parentId: ${draggedNodeType.parentId})
                 into ${targetNode.name} (segmentTypeId: ${targetNode.segmentTypeId}): ${canDrop}`);

    return canDrop;
  }
}
