{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "serve-template-app",
      "type": "shell",
      "command": "nx serve template-app --host=127.0.0.1 --verbose",
      "isBackground": true,
      "problemMatcher": {
        "owner": "typescript",
        "pattern": {
          "regexp": ".",
          "file": 1,
          "location": 2,
          "message": 3
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": ".",
          "endsPattern": "Compiled successfully|Failed to compile"
        }
      },
      "presentation": {
        "reveal": "always",
        "panel": "dedicated"
      }
    },
    {
      "label": "debug-protrac",
      "type": "npm",
      "script": "debug:protrac",
      "group": "build",
      "detail": "ng serve",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [
        {
          "owner": "typescript",
          "source": "ts",
          "applyTo": "closedDocuments",
          "fileLocation": [
            "relative",
            "${cwd}"
          ],
          "pattern": "$tsc",
          "background": {
            "activeOnStart": true,
            "beginsPattern": {
              "regexp": "(.*?)"
            },
            "endsPattern": {
              "regexp": "bundle generation complete|Compiled successfully|Failed to compile"
            }
          }
        }
      ]
    },
     {
      "label": "debug-template-app",
      "type": "npm",
      "script": "debug:tec-template",
      "group": "build",
      "detail": "ng serve",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [
        {
          "owner": "typescript",
          "source": "ts",
          "applyTo": "closedDocuments",
          "fileLocation": [
            "relative",
            "${cwd}"
          ],
          "pattern": "$tsc",
          "background": {
            "activeOnStart": true,
            "beginsPattern": {
              "regexp": "(.*?)"
            },
            "endsPattern": {
              "regexp": "bundle generation complete|Compiled successfully|Failed to compile"
            }
          }
        }
      ]
    },
    {
      "label": "debug-ed-admin",
      "type": "npm",
      "script": "debug:ed-admin",
      "group": "build",
      "detail": "ng serve",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [
        {
          "owner": "typescript",
          "source": "ts",
          "applyTo": "closedDocuments",
          "fileLocation": [
            "relative",
            "${cwd}"
          ],
          "pattern": "$tsc",
          "background": {
            "activeOnStart": true,
            "beginsPattern": {
              "regexp": "(.*?)"
            },
            "endsPattern": {
              "regexp": "bundle generation complete|Compiled successfully|Failed to compile"
            }
          }
        }
      ]
    },
    {
      "label": "debug-ed-pep",
      "type": "npm",
      "script": "debug:ed-pep",
      "group": "build",
      "detail": "ng serve",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [
        {
          "owner": "typescript",
          "source": "ts",
          "applyTo": "closedDocuments",
          "fileLocation": [
            "relative",
            "${cwd}"
          ],
          "pattern": "$tsc",
          "background": {
            "activeOnStart": true,
            "beginsPattern": {
              "regexp": "(.*?)"
            },
            "endsPattern": {
              "regexp": "bundle generation complete|Compiled successfully|Failed to compile"
            }
          }
        }
      ]
    },
  ]
}