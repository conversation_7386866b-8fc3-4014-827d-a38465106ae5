import { CommonModule } from '@angular/common';
import { Component, computed, inject, resource, signal, input, OnInit } from '@angular/core';
import { RadSignalBase } from '@tec/rad-core/services';
import { ScheduleManager } from './services/schedule-manager';
import { signalStore, withState } from '@ngrx/signals';
import { SegmentNode } from '@ed/shared/data-academics';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadPage } from '@tec/rad-ui/layout';
import { TreeGridAllModule } from '@syncfusion/ej2-angular-treegrid';
import { ScheduleListController } from './schedule-list-controller';
import { RadButtonModule } from '@tec/rad-ui/component';

const listStore = signalStore(withState({ nodes: [] as SegmentNode[] }));

@Component({
  selector: 'ed-schedule-list',
  imports: [CommonModule, RadPage, TreeGridAllModule, RadButtonModule],
  providers: [ScheduleListController],
  template: `
    <rad-page title="Schedule" [loading]="controller.isLoading()">
      <ejs-treegrid
        [dataSource]="data()"
        allowPaging="true"
        height="100%"
        idMapping="id"
        parentIdMapping="parentId"
        [treeColumnIndex]="0"
        rowHeight="50"
      >
        <e-columns>
          <e-column field="name" headerText="Task Name" width="170"></e-column>
          <e-column field="startDate" headerText="Start Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column field="endDate" headerText="End Date" width="130" format="yMd" textAlign="Left"></e-column>
          <e-column width="100">
            <ng-template #template let-data>
              @let item = data;
              <div class="flex flex-row justify-end">
                @if(data.containerType === 'Segment') {
                <p-button (click)="editSegment(data)" icon="ri-add-line" size="small" outlined="true" />
                }
              </div>
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-treegrid>
    </rad-page>
  `,
})
export class ScheduleList implements OnInit {
  protected controller = inject(ScheduleListController);
  #id = injectRouteParam('courseRunId');
  data = this.controller.nodes;

  ngOnInit(): void {
    this.controller.init(this.#id);
  }
}
