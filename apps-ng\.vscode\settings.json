{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "dist": true, "node_modules": false, ".angular": true, ".fttemplates": false, ".idea": true, ".nx": true, "tmp": true, "e2e": true, "libs/**/README.md": true, "*/**/{tsconfig*.json,README.md,jest.config.ts}": false, "*/**/{package.json,ng-package.json}": false}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/.nx/**": true}, "favorites.sortDirection": "ASC", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "$(capture).html, $(capture).scss, $(capture).module.ts, $(capture).spec.ts", "project.json": "tsconfig.*,jest.config.*,eslint.*,ng-package.json, README.MD"}, "explorer.fileNesting.expand": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "html", "json"], "angular.enable-strict-mode-prompt": false, "nxConsole.generateAiAgentRules": true, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.suggest.paths": true, "typescript.preferences.importModuleSpecifier": "shortest", "typescript.preferences.importModuleSpecifierEnding": "minimal", "debug.javascript.autoAttachFilter": "smart", "debug.javascript.terminalOptions": {"skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, "debug.javascript.autoAttachSmartPattern": ["${workspaceFolder}/**", "!**/node_modules/**", "**/$KNOWN_TOOLS$/**"], "typescript.preferences.autoImportFileExcludePatterns": ["**/.augment/**", "**/node_modules/@augment/**", "**/augment-*/**"]}